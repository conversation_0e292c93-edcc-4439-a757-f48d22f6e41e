<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>kin<PERSON> El - Alışverişin Yeni Adresi</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="manifest" href="manifest.json">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-shopping-bag"></i> İkinci El</h1>
                </div>
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Ürün, kategori veya marka ara...">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
                <nav class="nav-menu">
                    <button class="nav-btn" id="addProductBtn">
                        <i class="fas fa-plus"></i>
                        <span>İlan Ver</span>
                    </button>
                    <button class="nav-btn" id="messagesBtn">
                        <i class="fas fa-comments"></i>
                        <span class="badge" id="messageBadge">0</span>
                    </button>
                    <button class="nav-btn" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoriteBadge">0</span>
                    </button>
                    <button class="nav-btn" id="profileBtn">
                        <i class="fas fa-user"></i>
                    </button>
                    <button class="nav-btn theme-toggle" id="themeToggle" data-tooltip="Tema Değiştir">
                        <i class="fas fa-moon"></i>
                    </button>
                </nav>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <div class="mobile-menu-header">
            <h3>Menü</h3>
            <button class="close-btn" id="closeMobileMenu">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mobile-menu-content">
            <button class="mobile-menu-item" id="mobileAddProduct">
                <i class="fas fa-plus"></i> İlan Ver
            </button>
            <button class="mobile-menu-item" id="mobileMessages">
                <i class="fas fa-comments"></i> Mesajlar
                <span class="badge">0</span>
            </button>
            <button class="mobile-menu-item" id="mobileFavorites">
                <i class="fas fa-heart"></i> Favoriler
                <span class="badge">0</span>
            </button>
            <button class="mobile-menu-item" id="mobileProfile">
                <i class="fas fa-user"></i> Profilim
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Filters Sidebar -->
        <aside class="filters-sidebar" id="filtersSidebar">
            <div class="filter-header">
                <h3>Filtreler</h3>
                <button class="clear-filters" id="clearFilters">Temizle</button>
            </div>
            
            <div class="filter-section">
                <h4>Kategori</h4>
                <select id="categoryFilter" class="filter-select">
                    <option value="">Tüm Kategoriler</option>
                    <option value="elektronik">Elektronik</option>
                    <option value="ev-bahce">Ev & Bahçe</option>
                    <option value="moda">Moda</option>
                    <option value="arac">Araç</option>
                    <option value="kitap-hobi">Kitap & Hobi</option>
                    <option value="spor">Spor & Outdoor</option>
                    <option value="bebek-cocuk">Bebek & Çocuk</option>
                    <option value="diger">Diğer</option>
                </select>
            </div>

            <div class="filter-section">
                <h4>Fiyat Aralığı</h4>
                <div class="price-inputs">
                    <input type="number" id="minPrice" placeholder="Min" class="price-input">
                    <span>-</span>
                    <input type="number" id="maxPrice" placeholder="Max" class="price-input">
                </div>
            </div>

            <div class="filter-section">
                <h4>Konum</h4>
                <input type="text" id="locationFilter" placeholder="İl veya ilçe" class="filter-input">
            </div>

            <div class="filter-section">
                <h4>Sıralama</h4>
                <select id="sortFilter" class="filter-select">
                    <option value="newest">En Yeni</option>
                    <option value="oldest">En Eski</option>
                    <option value="price-low">Fiyat (Düşük-Yüksek)</option>
                    <option value="price-high">Fiyat (Yüksek-Düşük)</option>
                </select>
            </div>

            <button class="apply-filters" id="applyFilters">Filtreleri Uygula</button>
        </aside>

        <!-- Products Grid -->
        <section class="products-section">
            <!-- Hero Section -->
            <div class="hero-section">
                <div class="hero-content">
                    <h1>İkinci El Alışverişin Yeni Adresi</h1>
                    <p>Güvenli, hızlı ve kolay bir şekilde ikinci el ürün alın veya satın</p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-number">50K+</span>
                            <span class="stat-label">Aktif İlan</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">25K+</span>
                            <span class="stat-label">Mutlu Kullanıcı</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100+</span>
                            <span class="stat-label">Şehir</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Featured Categories -->
            <div class="featured-categories">
                <h2>Popüler Kategoriler</h2>
                <div class="categories-grid">
                    <div class="category-card" data-category="elektronik">
                        <i class="fas fa-laptop"></i>
                        <span>Elektronik</span>
                    </div>
                    <div class="category-card" data-category="moda">
                        <i class="fas fa-tshirt"></i>
                        <span>Moda</span>
                    </div>
                    <div class="category-card" data-category="ev-bahce">
                        <i class="fas fa-home"></i>
                        <span>Ev & Bahçe</span>
                    </div>
                    <div class="category-card" data-category="arac">
                        <i class="fas fa-car"></i>
                        <span>Araç</span>
                    </div>
                    <div class="category-card" data-category="spor">
                        <i class="fas fa-dumbbell"></i>
                        <span>Spor</span>
                    </div>
                    <div class="category-card" data-category="kitap-hobi">
                        <i class="fas fa-book"></i>
                        <span>Kitap & Hobi</span>
                    </div>
                </div>
            </div>

            <!-- Latest Products -->
            <div class="section-header">
                <h2>En Yeni İlanlar</h2>
                <button class="filter-toggle" id="filterToggle">
                    <i class="fas fa-filter"></i> Filtreler
                </button>
            </div>
            <div class="products-grid" id="latestProductsGrid">
                <!-- Latest products will be dynamically loaded here -->
            </div>

            <!-- Popular Products -->
            <div class="section-header">
                <h2>Popüler İlanlar</h2>
                <a href="#" class="view-all-link">Tümünü Gör <i class="fas fa-arrow-right"></i></a>
            </div>
            <div class="products-grid" id="popularProductsGrid">
                <!-- Popular products will be dynamically loaded here -->
            </div>

            <!-- All Products -->
            <div class="section-header">
                <h2>Tüm İlanlar</h2>
                <div class="section-controls">
                    <select id="sortSelect" class="sort-select">
                        <option value="newest">En Yeni</option>
                        <option value="oldest">En Eski</option>
                        <option value="price-low">Fiyat (Düşük-Yüksek)</option>
                        <option value="price-high">Fiyat (Yüksek-Düşük)</option>
                    </select>
                </div>
            </div>
            <div class="products-grid" id="productsGrid">
                <!-- All products will be dynamically loaded here -->
            </div>
        </section>
    </main>

    <!-- Add Product Modal -->
    <div class="modal" id="addProductModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Yeni İlan Ekle</h2>
                <button class="close-modal" data-modal="addProductModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addProductForm" class="product-form">
                <div class="form-group">
                    <label for="productTitle">İlan Başlığı</label>
                    <input type="text" id="productTitle" required>
                </div>

                <div class="form-group">
                    <label for="productCategory">Kategori</label>
                    <select id="productCategory" required>
                        <option value="">Kategori Seçin</option>
                        <option value="elektronik">Elektronik</option>
                        <option value="ev-bahce">Ev & Bahçe</option>
                        <option value="moda">Moda</option>
                        <option value="arac">Araç</option>
                        <option value="kitap-hobi">Kitap & Hobi</option>
                        <option value="spor">Spor & Outdoor</option>
                        <option value="bebek-cocuk">Bebek & Çocuk</option>
                        <option value="diger">Diğer</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="productPrice">Fiyat (₺)</label>
                    <input type="number" id="productPrice" required>
                </div>

                <div class="form-group">
                    <label for="productDescription">Açıklama</label>
                    <textarea id="productDescription" rows="4" required></textarea>
                </div>

                <div class="form-group">
                    <label for="productLocation">Konum</label>
                    <input type="text" id="productLocation" placeholder="İl, İlçe" required>
                </div>

                <div class="form-group">
                    <label for="productImages">Fotoğraflar (Max 5)</label>
                    <input type="file" id="productImages" multiple accept="image/*" class="file-input">
                    <div class="image-preview" id="imagePreview"></div>
                </div>

                <button type="submit" class="submit-btn">İlanı Yayınla</button>
            </form>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal" id="productDetailModal">
        <div class="modal-content product-detail">
            <div class="modal-header">
                <h2 id="detailTitle">Ürün Detayı</h2>
                <button class="close-modal" data-modal="productDetailModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="product-detail-content" id="productDetailContent">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Messages Modal -->
    <div class="modal" id="messagesModal">
        <div class="modal-content messages-modal">
            <div class="modal-header">
                <h2>Mesajlar</h2>
                <button class="close-modal" data-modal="messagesModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="messages-container">
                <div class="conversations-list" id="conversationsList">
                    <!-- Conversations will be loaded here -->
                </div>
                <div class="chat-area" id="chatArea">
                    <div class="chat-placeholder">
                        <i class="fas fa-comments"></i>
                        <p>Bir sohbet seçin</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login/Register Modal -->
    <div class="modal" id="authModal">
        <div class="modal-content auth-modal">
            <div class="modal-header">
                <h2 id="authTitle">Giriş Yap</h2>
                <button class="close-modal" data-modal="authModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="auth-tabs">
                <button class="auth-tab active" data-tab="login">Giriş Yap</button>
                <button class="auth-tab" data-tab="register">Kayıt Ol</button>
            </div>
            
            <!-- Login Form -->
            <form id="loginForm" class="auth-form active">
                <div class="form-group">
                    <label for="loginEmail">E-posta</label>
                    <input type="email" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Şifre</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <button type="submit" class="submit-btn">Giriş Yap</button>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label for="registerName">Ad Soyad</label>
                    <input type="text" id="registerName" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">E-posta</label>
                    <input type="email" id="registerEmail" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">Şifre</label>
                    <input type="password" id="registerPassword" required>
                </div>
                <div class="form-group">
                    <label for="registerPhone">Telefon</label>
                    <input type="tel" id="registerPhone" placeholder="5XX XXX XX XX" required>
                </div>
                <button type="submit" class="submit-btn">Kayıt Ol</button>
            </form>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal" id="profileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Profilim</h2>
                <button class="close-modal" data-modal="profileModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="profile-content" id="profileContent">
                <!-- Profile content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast" id="toast"></div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/products.js"></script>
    <script src="js/messages.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
