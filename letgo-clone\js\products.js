// Products Module

const products = {
    currentFilter: {
        category: '',
        minPrice: null,
        maxPrice: null,
        location: '',
        sort: 'newest'
    },

    // Initialize products
    init() {
        this.setupEventListeners();
        this.loadProducts();
        this.initializeSampleData();
    },

    // Initialize sample data if no products exist
    initializeSampleData() {
        const existingProducts = utils.storage.get('products');
        if (!existingProducts || existingProducts.length === 0) {
            const sampleProducts = [
                {
                    id: utils.generateId(),
                    title: 'iPhone 12 Pro 128GB',
                    category: 'elektronik',
                    price: 25000,
                    description: 'Te<PERSON>z kullanılmış, kutulu, faturalı iPhone 12 Pro. Kamera ve batarya performansı mükemmel.',
                    location: 'İstanbul, Kadıköy',
                    images: ['data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9IjIwMCIgeT0iMTUwIiBzdHlsZT0iZmlsbDojYWFhO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1zaXplOjI1cHg7Zm9udC1mYW1pbHk6QXJpYWwsSGVsdmV0aWNhLHNhbnMtc2VyaWY7ZG9taW5hbnQtYmFzZWxpbmU6Y2VudHJhbCI+aVBob25lIDEyIFBybzwvdGV4dD48L3N2Zz4='],
                    userId: 'sample-user-1',
                    userName: '<PERSON><PERSON>ılmaz',
                    userPhone: '5551234567',
                    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                    views: 145
                },
                {
                    id: utils.generateId(),
                    title: 'Ikea Ektorp 3 Kişilik Kanepe',
                    category: 'ev-bahce',
                    price: 3500,
                    description: '2 yıllık, çok temiz kullanılmış kanepe. Kılıfı yıkanabilir. Taşınma nedeniyle satılıktır.',
                    location: 'Ankara, Çankaya',
                    images: ['data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9IjIwMCIgeT0iMTUwIiBzdHlsZT0iZmlsbDojYWFhO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1zaXplOjI1cHg7Zm9udC1mYW1pbHk6QXJpYWwsSGVsdmV0aWNhLHNhbnMtc2VyaWY7ZG9taW5hbnQtYmFzZWxpbmU6Y2VudHJhbCI+S2FuZXBlPC90ZXh0Pjwvc3ZnPg=='],
                    userId: 'sample-user-2',
                    userName: 'Ayşe Demir',
                    userPhone: '5559876543',
                    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                    views: 89
                },
                {
                    id: utils.generateId(),
                    title: 'Nike Air Max 270 - 42 Numara',
                    category: 'moda',
                    price: 800,
                    description: 'Orijinal Nike Air Max 270. 3 kez giyildi, sıfır ayarında. Kutulu.',
                    location: 'İzmir, Karşıyaka',
                    images: ['data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9IjIwMCIgeT0iMTUwIiBzdHlsZT0iZmlsbDojYWFhO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1zaXplOjI1cHg7Zm9udC1mYW1pbHk6QXJpYWwsSGVsdmV0aWNhLHNhbnMtc2VyaWY7ZG9taW5hbnQtYmFzZWxpbmU6Y2VudHJhbCI+TmlrZSBBaXIgTWF4PC90ZXh0Pjwvc3ZnPg=='],
                    userId: 'sample-user-3',
                    userName: 'Mehmet Kaya',
                    userPhone: '5553334455',
                    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    views: 67
                },
                {
                    id: utils.generateId(),
                    title: 'PlayStation 5 + 2 Oyun',
                    category: 'elektronik',
                    price: 15000,
                    description: 'PS5 konsol + 2 kol + Spider-Man ve FIFA 23 oyunları. Garantisi devam ediyor.',
                    location: 'Bursa, Nilüfer',
                    images: ['data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9IjIwMCIgeT0iMTUwIiBzdHlsZT0iZmlsbDojYWFhO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1zaXplOjI1cHg7Zm9udC1mYW1pbHk6QXJpYWwsSGVsdmV0aWNhLHNhbnMtc2VyaWY7ZG9taW5hbnQtYmFzZWxpbmU6Y2VudHJhbCI+UGxheVN0YXRpb24gNTwvdGV4dD48L3N2Zz4='],
                    userId: 'sample-user-4',
                    userName: 'Fatma Öz',
                    userPhone: '5557778899',
                    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
                    views: 234
                },
                {
                    id: utils.generateId(),
                    title: 'Bisiklet - 26 Jant Dağ Bisikleti',
                    category: 'spor',
                    price: 2200,
                    description: '21 vites Salcano dağ bisikleti. Yeni lastikler takıldı, bakımı yapıldı.',
                    location: 'Antalya, Muratpaşa',
                    images: ['data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9IjIwMCIgeT0iMTUwIiBzdHlsZT0iZmlsbDojYWFhO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1zaXplOjI1cHg7Zm9udC1mYW1pbHk6QXJpYWwsSGVsdmV0aWNhLHNhbnMtc2VyaWY7ZG9taW5hbnQtYmFzZWxpbmU6Y2VudHJhbCI+QmlzaWtsZXQ8L3RleHQ+PC9zdmc+'],
                    userId: 'sample-user-5',
                    userName: 'Ali Çelik',
                    userPhone: '5552223344',
                    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                    views: 156
                }
            ];

            utils.storage.set('products', sampleProducts);
        }
    },

    // Setup event listeners
    setupEventListeners() {
        // Add product button
        document.getElementById('addProductBtn').addEventListener('click', () => {
            utils.requireAuth(() => utils.modal.open('addProductModal'));
        });

        // Mobile add product
        document.getElementById('mobileAddProduct').addEventListener('click', () => {
            document.getElementById('mobileMenu').classList.remove('active');
            utils.requireAuth(() => utils.modal.open('addProductModal'));
        });

        // Add product form
        const addProductForm = document.getElementById('addProductForm');
        if (addProductForm) {
            addProductForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddProduct();
            });
        }

        // Image upload
        const imageInput = document.getElementById('productImages');
        if (imageInput) {
            imageInput.addEventListener('change', (e) => {
                this.handleImageSelection(e.target);
            });
        }

        // Search
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.querySelector('.search-btn');
        
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });
        }

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.handleSearch();
            });
        }

        // Filters
        document.getElementById('applyFilters').addEventListener('click', () => {
            this.applyFilters();
        });

        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // Filter toggle for mobile
        document.getElementById('filterToggle').addEventListener('click', () => {
            document.getElementById('filtersSidebar').classList.toggle('active');
        });

        // Favorites button
        document.getElementById('favoritesBtn').addEventListener('click', () => {
            utils.requireAuth(() => this.showFavorites());
        });

        document.getElementById('mobileFavorites').addEventListener('click', () => {
            document.getElementById('mobileMenu').classList.remove('active');
            utils.requireAuth(() => this.showFavorites());
        });

        // Product card clicks
        document.addEventListener('click', (e) => {
            const productCard = e.target.closest('.product-card');
            if (productCard && !e.target.closest('.favorite-btn')) {
                this.showProductDetail(productCard.dataset.productId);
            }

            // Favorite button
            if (e.target.closest('.favorite-btn')) {
                e.preventDefault();
                e.stopPropagation();
                utils.requireAuth(() => {
                    this.toggleFavorite(e.target.closest('.favorite-btn').dataset.productId);
                });
            }

            // Remove image button
            if (e.target.closest('.remove-image')) {
                e.target.closest('.preview-image').remove();
            }
        });
    },

    // Load latest products
    loadLatestProducts() {
        const products = utils.storage.get('products') || [];
        const latestProducts = products
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 6);
        this.displayProductsInGrid(latestProducts, 'latestProductsGrid');
    },

    // Load popular products
    loadPopularProducts() {
        const products = utils.storage.get('products') || [];
        const popularProducts = products
            .sort((a, b) => (b.views || 0) - (a.views || 0))
            .slice(0, 6);
        this.displayProductsInGrid(popularProducts, 'popularProductsGrid');
    },

    // Load and display products
    loadProducts(filter = null) {
        const products = utils.storage.get('products') || [];
        let filteredProducts = [...products];

        // Apply search filter
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(p =>
                p.title.toLowerCase().includes(searchTerm) ||
                p.description.toLowerCase().includes(searchTerm) ||
                p.location.toLowerCase().includes(searchTerm)
            );
        }

        // Apply filters
        if (filter || this.currentFilter) {
            const activeFilter = filter || this.currentFilter;

            if (activeFilter.category) {
                filteredProducts = filteredProducts.filter(p => p.category === activeFilter.category);
            }

            if (activeFilter.minPrice) {
                filteredProducts = filteredProducts.filter(p => p.price >= activeFilter.minPrice);
            }

            if (activeFilter.maxPrice) {
                filteredProducts = filteredProducts.filter(p => p.price <= activeFilter.maxPrice);
            }

            if (activeFilter.location) {
                filteredProducts = filteredProducts.filter(p =>
                    p.location.toLowerCase().includes(activeFilter.location.toLowerCase())
                );
            }
        }

        // Sort products
        const sortBy = this.currentFilter.sort || 'newest';
        filteredProducts.sort((a, b) => {
            switch (sortBy) {
                case 'newest':
                    return new Date(b.createdAt) - new Date(a.createdAt);
                case 'oldest':
                    return new Date(a.createdAt) - new Date(b.createdAt);
                case 'price-low':
                    return a.price - b.price;
                case 'price-high':
                    return b.price - a.price;
                default:
                    return 0;
            }
        });

        this.displayProducts(filteredProducts);
    },

    // Search products
    searchProducts(query) {
        document.getElementById('searchInput').value = query;
        this.loadProducts();
    },

    // Sort products
    sortProducts(sortBy) {
        this.currentFilter.sort = sortBy;
        this.loadProducts();
    },

    // Filter by category
    filterByCategory(category) {
        this.currentFilter.category = category;
        document.getElementById('categoryFilter').value = category;
        this.loadProducts();

        // Scroll to products section
        document.querySelector('.products-section').scrollIntoView({
            behavior: 'smooth'
        });
    },

    // Display products in specific grid
    displayProductsInGrid(products, gridId) {
        const grid = document.getElementById(gridId);
        if (!grid) return;

        const userFavorites = this.getUserFavorites();

        if (products.length === 0) {
            grid.innerHTML = `
                <div class="empty-state" style="grid-column: 1/-1;">
                    <i class="fas fa-box-open"></i>
                    <h3>Ürün bulunamadı</h3>
                    <p>Bu kategoride henüz ürün bulunmuyor.</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = products.map(product => {
            const isFavorite = userFavorites.includes(product.id);

            return `
                <div class="product-card" data-product-id="${product.id}">
                    <img src="${product.images[0] || 'images/placeholder.svg'}"
                         alt="${product.title}"
                         class="product-image"
                         loading="lazy">
                    <div class="product-info">
                        <h3 class="product-title">${product.title}</h3>
                        <div class="product-price">${utils.formatPrice(product.price)}</div>
                        <div class="product-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${product.location}</span>
                        </div>
                        <div class="product-date">${utils.formatDate(product.createdAt)}</div>
                        <div class="product-actions">
                            <span class="views">
                                <i class="fas fa-eye"></i> ${product.views || 0}
                            </span>
                            <button class="favorite-btn ${isFavorite ? 'active' : ''}"
                                    data-product-id="${product.id}"
                                    title="${isFavorite ? 'Favorilerden çıkar' : 'Favorilere ekle'}">
                                <i class="${isFavorite ? 'fas' : 'far'} fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    },

    // Display products in grid
    displayProducts(products) {
        this.displayProductsInGrid(products, 'productsGrid');
    },

    // Handle search
    handleSearch() {
        this.loadProducts();
    },

    // Apply filters
    applyFilters() {
        this.currentFilter = {
            category: document.getElementById('categoryFilter').value,
            minPrice: document.getElementById('minPrice').value ? 
                      parseInt(document.getElementById('minPrice').value) : null,
            maxPrice: document.getElementById('maxPrice').value ? 
                      parseInt(document.getElementById('maxPrice').value) : null,
            location: document.getElementById('locationFilter').value,
            sort: document.getElementById('sortFilter').value
        };

        this.loadProducts();
        
        // Close mobile filter sidebar
        document.getElementById('filtersSidebar').classList.remove('active');
    },

    // Clear filters
    clearFilters() {
        document.getElementById('categoryFilter').value = '';
        document.getElementById('minPrice').value = '';
        document.getElementById('maxPrice').value = '';
        document.getElementById('locationFilter').value = '';
        document.getElementById('sortFilter').value = 'newest';
        
        this.currentFilter = {
            category: '',
            minPrice: null,
            maxPrice: null,
            location: '',
            sort: 'newest'
        };
        
        this.loadProducts();
    },

    // Handle image selection
    handleImageSelection(input) {
        utils.handleImageUpload(input, (images) => {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';
            
            images.forEach((image, index) => {
                const div = document.createElement('div');
                div.className = 'preview-image';
                div.innerHTML = `
                    <img src="${image}" alt="Preview ${index + 1}">
                    <button type="button" class="remove-image">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                preview.appendChild(div);
            });
        });
    },

    // Handle add product
    handleAddProduct() {
        const user = utils.getCurrentUser();
        if (!user) return;

        const title = document.getElementById('productTitle').value;
        const category = document.getElementById('productCategory').value;
        const price = parseInt(document.getElementById('productPrice').value);
        const description = document.getElementById('productDescription').value;
        const location = document.getElementById('productLocation').value;
        
        // Get images from preview
        const images = Array.from(document.querySelectorAll('#imagePreview img'))
                           .map(img => img.src);

        if (images.length === 0) {
            utils.showToast('En az bir fotoğraf eklemelisiniz!', 'error');
            return;
        }

        const newProduct = {
            id: utils.generateId(),
            title,
            category,
            price,
            description,
            location,
            images,
            userId: user.id,
            userName: user.name,
            userPhone: user.phone,
            createdAt: new Date().toISOString(),
            views: 0
        };

        // Save product
        const products = utils.storage.get('products') || [];
        products.unshift(newProduct);
        utils.storage.set('products', products);

        // Reset form and close modal
        document.getElementById('addProductForm').reset();
        document.getElementById('imagePreview').innerHTML = '';
        utils.modal.close('addProductModal');
        
        utils.showToast('İlanınız başarıyla yayınlandı!');
        this.loadProducts();
    },

    // Show product detail
    showProductDetail(productId) {
        const products = utils.storage.get('products') || [];
        const product = products.find(p => p.id === productId);
        
        if (!product) return;

        // Increment views
        product.views = (product.views || 0) + 1;
        utils.storage.set('products', products);

        const currentUser = utils.getCurrentUser();
        const isOwner = currentUser && product.userId === currentUser.id;
        const userFavorites = this.getUserFavorites();
        const isFavorite = userFavorites.includes(product.id);

        const detailContent = `
            <div class="detail-images">
                <img src="${product.images[0]}" alt="${product.title}" class="main-image" id="mainImage">
                ${product.images.length > 1 ? `
                    <div class="thumbnail-images">
                        ${product.images.map((img, index) => `
                            <img src="${img}" 
                                 alt="Thumbnail ${index + 1}" 
                                 class="thumbnail ${index === 0 ? 'active' : ''}"
                                 onclick="products.changeMainImage('${img}', this)">
                        `).join('')}
                    </div>
                ` : ''}
            </div>
            
            <div class="detail-info">
                <h2>${product.title}</h2>
                <div class="detail-price">${utils.formatPrice(product.price)}</div>
                
                <div class="detail-meta">
                    <div class="meta-item">
                        <i class="fas fa-tag"></i>
                        <span>${this.getCategoryName(product.category)}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${product.location}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>${utils.formatDate(product.createdAt)}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <span>${product.views} görüntülenme</span>
                    </div>
                </div>
                
                <div class="detail-description">
                    <h3>Açıklama</h3>
                    <p>${product.description}</p>
                </div>
                
                ${!isOwner ? `
                    <div class="seller-info">
                        <h3>Satıcı Bilgileri</h3>
                        <div class="seller-details">
                            <div class="seller-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h4>${product.userName}</h4>
                                <p>Üye</p>
                            </div>
                        </div>
                        <div class="contact-buttons">
                            <button class="contact-btn message-btn" onclick="products.contactSeller('${product.id}')">
                                <i class="fas fa-comment"></i> Mesaj Gönder
                            </button>
                            <button class="contact-btn call-btn" onclick="products.showPhone('${product.userPhone}')">
                                <i class="fas fa-phone"></i> Ara
                            </button>
                        </div>
                    </div>
                ` : `
                    <div class="owner-actions">
                        <button class="submit-btn" onclick="products.editProduct('${product.id}')">
                            <i class="fas fa-edit"></i> İlanı Düzenle
                        </button>
                        <button class="submit-btn" style="background-color: var(--danger-color);" 
                                onclick="products.deleteProduct('${product.id}')">
                            <i class="fas fa-trash"></i> İlanı Sil
                        </button>
                    </div>
                `}
                
                ${!isOwner ? `
                    <button class="favorite-btn ${isFavorite ? 'active' : ''}" 
                            style="margin-top: 1rem; font-size: 1rem; padding: 0.75rem 1.5rem;"
                            onclick="products.toggleFavorite('${product.id}')">
                        <i class="${isFavorite ? 'fas' : 'far'} fa-heart"></i>
                        ${isFavorite ? 'Favorilerden Çıkar' : 'Favorilere Ekle'}
                    </button>
                ` : ''}
            </div>
        `;

        document.getElementById('productDetailContent').innerHTML = detailContent;
        document.getElementById('detailTitle').textContent = product.title;
        utils.modal.open('productDetailModal');
    },

    // Change main image in detail view
    changeMainImage(src, thumbnail) {
        document.getElementById('mainImage').src = src;
        document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
        thumbnail.classList.add('active');
    },

    // Get category name
    getCategoryName(category) {
        const categories = {
            'elektronik': 'Elektronik',
            'ev-bahce': 'Ev & Bahçe',
            'moda': 'Moda',
            'arac': 'Araç',
            'kitap-hobi': 'Kitap & Hobi',
            'spor': 'Spor & Outdoor',
            'bebek-cocuk': 'Bebek & Çocuk',
            'diger': 'Diğer'
        };
        return categories[category] || category;
    },

    // Contact seller
    contactSeller(productId) {
        utils.requireAuth(() => {
            utils.modal.close('productDetailModal');
            if (window.messages) {
                window.messages.startConversation(productId);
            }
        });
    },

    // Show phone number
    showPhone(phone) {
        utils.showToast(`Telefon: ${phone}`, 'success');
    },

    // Edit product (placeholder)
    editProduct(productId) {
        utils.showToast('Bu özellik yakında eklenecek!', 'warning');
    },

    // Delete product
    deleteProduct(productId) {
        if (confirm('Bu ilanı silmek istediğinizden emin misiniz?')) {
            const products = utils.storage.get('products') || [];
            const index = products.findIndex(p => p.id === productId);
            
            if (index > -1) {
                products.splice(index, 1);
                utils.storage.set('products', products);
                utils.modal.close('productDetailModal');
                utils.showToast('İlan başarıyla silindi');
                this.loadProducts();
            }
        }
    },

    // Toggle favorite
    toggleFavorite(productId) {
        const user = utils.getCurrentUser();
        if (!user) return;

        const users = utils.storage.get('users') || [];
        const userIndex = users.findIndex(u => u.id === user.id);
        
        if (userIndex > -1) {
            if (!users[userIndex].favorites) {
                users[userIndex].favorites = [];
            }
            
            const favIndex = users[userIndex].favorites.indexOf(productId);
            
            if (favIndex > -1) {
                users[userIndex].favorites.splice(favIndex, 1);
                utils.showToast('Favorilerden çıkarıldı');
            } else {
                users[userIndex].favorites.push(productId);
                utils.showToast('Favorilere eklendi');
            }
            
            utils.storage.set('users', users);
            
            // Update UI
            this.loadProducts();
            if (window.auth) {
                window.auth.updateFavoritesCount();
            }
            
            // Update detail view if open
            const detailContent = document.getElementById('productDetailContent');
            if (detailContent && detailContent.innerHTML.includes(productId)) {
                this.showProductDetail(productId);
            }
        }
    },

    // Get user favorites
    getUserFavorites() {
        const user = utils.getCurrentUser();
        if (!user) return [];
        
        const users = utils.storage.get('users') || [];
        const fullUser = users.find(u => u.id === user.id);
        
        return fullUser ? (fullUser.favorites || []) : [];
    },

    // Show favorites
    showFavorites() {
        const favorites = this.getUserFavorites();
        const products = utils.storage.get('products') || [];
        const favoriteProducts = products.filter(p => favorites.includes(p.id));
        
        document.querySelector('.section-header h2').textContent = 'Favorilerim';
        this.displayProducts(favoriteProducts);
    },

    // Show user products
    showUserProducts() {
        const user = utils.getCurrentUser();
        if (!user) return;
        
        const products = utils.storage.get('products') || [];
        const userProducts = products.filter(p => p.userId === user.id);
        
        document.querySelector('.section-header h2').textContent = 'İlanlarım';
        this.displayProducts(userProducts);
    }
};

// Initialize products when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    products.init();
});

// Export products object
window.products = products;
