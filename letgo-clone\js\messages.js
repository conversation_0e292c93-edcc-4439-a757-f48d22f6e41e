// Messages Module

const messages = {
    currentConversation: null,

    // Initialize messages
    init() {
        this.setupEventListeners();
        this.initializeSampleMessages();
    },

    // Initialize sample messages
    initializeSampleMessages() {
        const existingMessages = utils.storage.get('messages');
        if (!existingMessages) {
            utils.storage.set('messages', []);
        }

        const existingConversations = utils.storage.get('conversations');
        if (!existingConversations) {
            utils.storage.set('conversations', []);
        }
    },

    // Setup event listeners
    setupEventListeners() {
        // Messages button
        document.getElementById('messagesBtn').addEventListener('click', () => {
            utils.requireAuth(() => this.openMessages());
        });

        // Mobile messages button
        document.getElementById('mobileMessages').addEventListener('click', () => {
            document.getElementById('mobileMenu').classList.remove('active');
            utils.requireAuth(() => this.openMessages());
        });

        // Conversation clicks
        document.addEventListener('click', (e) => {
            const conversationItem = e.target.closest('.conversation-item');
            if (conversationItem) {
                this.openConversation(conversationItem.dataset.conversationId);
            }
        });

        // Send message
        document.addEventListener('keypress', (e) => {
            if (e.target.id === 'messageInput' && e.key === 'Enter') {
                this.sendMessage();
            }
        });
    },

    // Open messages modal
    openMessages() {
        this.loadConversations();
        utils.modal.open('messagesModal');
    },

    // Load conversations
    loadConversations() {
        const user = utils.getCurrentUser();
        if (!user) return;

        const conversations = utils.storage.get('conversations') || [];
        const userConversations = conversations.filter(c => 
            c.participants.includes(user.id)
        );

        const conversationsList = document.getElementById('conversationsList');
        
        if (userConversations.length === 0) {
            conversationsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <p>Henüz mesajınız yok</p>
                </div>
            `;
            return;
        }

        conversationsList.innerHTML = userConversations
            .sort((a, b) => new Date(b.lastMessageTime) - new Date(a.lastMessageTime))
            .map(conversation => {
                const otherUserId = conversation.participants.find(id => id !== user.id);
                const otherUser = this.getUserById(otherUserId);
                const product = this.getProductById(conversation.productId);
                
                return `
                    <div class="conversation-item ${conversation.id === this.currentConversation ? 'active' : ''}" 
                         data-conversation-id="${conversation.id}">
                        <div class="conversation-header">
                            <div class="conversation-name">${otherUser ? otherUser.name : 'Kullanıcı'}</div>
                            <div class="conversation-time">${utils.formatDate(conversation.lastMessageTime)}</div>
                        </div>
                        <div class="conversation-preview">
                            ${product ? product.title : 'Ürün'} - ${conversation.lastMessage}
                        </div>
                        ${conversation.unreadCount > 0 ? `
                            <span class="unread-badge">${conversation.unreadCount}</span>
                        ` : ''}
                    </div>
                `;
            }).join('');

        this.updateMessageCount();
    },

    // Open specific conversation
    openConversation(conversationId) {
        const user = utils.getCurrentUser();
        if (!user) return;

        this.currentConversation = conversationId;
        
        const conversations = utils.storage.get('conversations') || [];
        const conversation = conversations.find(c => c.id === conversationId);
        
        if (!conversation) return;

        // Mark messages as read
        conversation.unreadCount = 0;
        utils.storage.set('conversations', conversations);
        
        // Update conversation list UI
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.classList.toggle('active', item.dataset.conversationId === conversationId);
        });

        // Load messages
        const messages = utils.storage.get('messages') || [];
        const conversationMessages = messages.filter(m => m.conversationId === conversationId);
        
        const otherUserId = conversation.participants.find(id => id !== user.id);
        const otherUser = this.getUserById(otherUserId);
        const product = this.getProductById(conversation.productId);

        const chatArea = document.getElementById('chatArea');
        chatArea.innerHTML = `
            <div class="chat-header">
                <h4>${otherUser ? otherUser.name : 'Kullanıcı'}</h4>
                <p>${product ? product.title : 'Ürün'}</p>
            </div>
            <div class="chat-messages" id="chatMessages">
                ${conversationMessages.map(message => `
                    <div class="message ${message.senderId === user.id ? 'sent' : 'received'}">
                        <div class="message-bubble">
                            ${message.text}
                            <div class="message-time">${utils.formatDate(message.timestamp)}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Mesajınızı yazın...">
                <button class="send-btn" onclick="messages.sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        `;

        // Scroll to bottom
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        this.updateMessageCount();
    },

    // Send message
    sendMessage() {
        const user = utils.getCurrentUser();
        if (!user || !this.currentConversation) return;

        const input = document.getElementById('messageInput');
        const text = input.value.trim();
        
        if (!text) return;

        const newMessage = {
            id: utils.generateId(),
            conversationId: this.currentConversation,
            senderId: user.id,
            text: text,
            timestamp: new Date().toISOString()
        };

        // Save message
        const messages = utils.storage.get('messages') || [];
        messages.push(newMessage);
        utils.storage.set('messages', messages);

        // Update conversation
        const conversations = utils.storage.get('conversations') || [];
        const conversation = conversations.find(c => c.id === this.currentConversation);
        
        if (conversation) {
            conversation.lastMessage = text;
            conversation.lastMessageTime = newMessage.timestamp;
            utils.storage.set('conversations', conversations);
        }

        // Clear input
        input.value = '';

        // Add message to UI
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message sent';
        messageDiv.innerHTML = `
            <div class="message-bubble">
                ${text}
                <div class="message-time">${utils.formatDate(newMessage.timestamp)}</div>
            </div>
        `;
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Update conversation list
        this.loadConversations();
    },

    // Start new conversation
    startConversation(productId) {
        const user = utils.getCurrentUser();
        if (!user) return;

        const products = utils.storage.get('products') || [];
        const product = products.find(p => p.id === productId);
        
        if (!product || product.userId === user.id) {
            utils.showToast('Kendi ilanınıza mesaj gönderemezsiniz!', 'error');
            return;
        }

        // Check if conversation already exists
        const conversations = utils.storage.get('conversations') || [];
        let conversation = conversations.find(c => 
            c.productId === productId && 
            c.participants.includes(user.id) && 
            c.participants.includes(product.userId)
        );

        if (!conversation) {
            // Create new conversation
            conversation = {
                id: utils.generateId(),
                productId: productId,
                participants: [user.id, product.userId],
                lastMessage: 'Yeni sohbet başlatıldı',
                lastMessageTime: new Date().toISOString(),
                unreadCount: 0
            };
            
            conversations.push(conversation);
            utils.storage.set('conversations', conversations);

            // Create initial message
            const initialMessage = {
                id: utils.generateId(),
                conversationId: conversation.id,
                senderId: user.id,
                text: `Merhaba, "${product.title}" ilanınız hakkında bilgi almak istiyorum.`,
                timestamp: new Date().toISOString()
            };

            const messages = utils.storage.get('messages') || [];
            messages.push(initialMessage);
            utils.storage.set('messages', messages);

            conversation.lastMessage = initialMessage.text;
        }

        // Open messages modal and conversation
        this.openMessages();
        setTimeout(() => {
            this.openConversation(conversation.id);
        }, 100);
    },

    // Update message count badge
    updateMessageCount() {
        const user = utils.getCurrentUser();
        if (!user) return;

        const conversations = utils.storage.get('conversations') || [];
        const userConversations = conversations.filter(c => 
            c.participants.includes(user.id)
        );

        const unreadCount = userConversations.reduce((sum, c) => sum + (c.unreadCount || 0), 0);
        
        document.getElementById('messageBadge').textContent = unreadCount;
        document.querySelector('#mobileMessages .badge').textContent = unreadCount;
    },

    // Helper functions
    getUserById(userId) {
        const users = utils.storage.get('users') || [];
        return users.find(u => u.id === userId);
    },

    getProductById(productId) {
        const products = utils.storage.get('products') || [];
        return products.find(p => p.id === productId);
    }
};

// Initialize messages when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    messages.init();
});

// Export messages object
window.messages = messages;
