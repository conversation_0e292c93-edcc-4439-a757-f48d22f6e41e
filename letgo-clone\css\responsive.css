/* Modern Responsive Styles */

/* Large Tablet Styles (768px - 1024px) */
@media (max-width: 1024px) {
    .main-content {
        gap: var(--space-4);
    }

    .filters-sidebar {
        width: 240px;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: var(--space-6);
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-stats {
        gap: var(--space-8);
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: var(--space-4);
    }

    .messages-modal .modal-content {
        max-width: 100%;
        margin: var(--space-4);
    }
}

/* Mobile Styles (max-width: 768px) */
@media (max-width: 768px) {
    /* Header */
    .header-content {
        flex-wrap: wrap;
        gap: var(--space-4);
        padding: var(--space-3) 0;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .search-bar {
        order: 3;
        flex-basis: 100%;
        max-width: 100%;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    /* Hero Section */
    .hero-section {
        padding: var(--space-12) var(--space-6);
        margin-bottom: var(--space-8);
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
        margin-bottom: var(--space-6);
    }

    .hero-stats {
        gap: var(--space-6);
        flex-direction: column;
    }

    .stat-number {
        font-size: 2rem;
    }

    /* Categories */
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-4);
    }

    .category-card {
        padding: var(--space-6) var(--space-3);
    }

    .category-card i {
        font-size: 2rem;
        margin-bottom: var(--space-3);
    }

    .category-card span {
        font-size: 0.8125rem;
    }

    /* Main Content */
    .main-content {
        flex-direction: column;
        padding: 0 var(--space-4);
        margin: var(--space-4) auto;
    }

    /* Filters */
    .filters-sidebar {
        display: none;
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2000;
        border-radius: 0;
        max-height: 100vh;
        overflow-y: auto;
    }

    .filters-sidebar.active {
        display: block;
    }

    .filter-toggle {
        display: flex;
        align-items: center;
        gap: var(--space-2);
    }

    /* Products Grid */
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: var(--space-4);
    }

    .product-card {
        border-radius: var(--radius-lg);
    }

    .product-image {
        height: 180px;
    }

    .product-info {
        padding: var(--space-4);
    }

    .product-title {
        font-size: 0.95rem;
    }

    .product-price {
        font-size: 1.25rem;
    }

    .product-location,
    .product-date {
        font-size: 0.8125rem;
    }

    /* Modals */
    .modal-content {
        width: 95%;
        margin: 1rem;
        max-height: 95vh;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    /* Product Detail */
    .main-image {
        height: 250px;
    }

    .detail-price {
        font-size: 1.5rem;
    }

    .detail-meta {
        gap: 1rem;
    }

    .contact-buttons {
        flex-direction: column;
    }

    /* Messages */
    .messages-modal .modal-content {
        height: 90vh;
    }

    .messages-container {
        flex-direction: column;
    }

    .conversations-list {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--light-color);
    }

    .chat-area {
        height: calc(100% - 200px);
    }

    .message-bubble {
        max-width: 85%;
    }

    /* Forms */
    .product-form,
    .auth-form {
        padding: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Profile */
    .profile-stats {
        grid-template-columns: 1fr;
    }

    /* Toast */
    .toast {
        left: 20px;
        right: 20px;
        bottom: 20px;
    }
}

/* Small Mobile Styles (max-width: 480px) */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .header-content {
        padding: 0.75rem 0;
    }

    .logo h1 {
        font-size: 1.3rem;
    }

    .logo h1 i {
        display: none;
    }

    .search-bar input {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .search-btn {
        padding: 0 1rem;
    }

    /* Products Grid */
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .product-image {
        height: 140px;
    }

    .product-info {
        padding: 0.5rem;
    }

    .product-title {
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
    }

    .product-price {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .product-actions {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
    }

    /* Modals */
    .modal-content {
        border-radius: 10px 10px 0 0;
        margin: 0;
        width: 100%;
        position: absolute;
        bottom: 0;
        max-height: 90vh;
    }

    /* Product Detail */
    .main-image {
        height: 200px;
        border-radius: 0;
        margin: -1.5rem -1.5rem 1rem;
    }

    .thumbnail-images {
        padding: 0 1.5rem;
    }

    .detail-info {
        padding: 0 1.5rem 1.5rem;
    }

    /* Messages on very small screens */
    .conversations-list {
        height: 150px;
    }

    .conversation-item {
        padding: 0.75rem;
    }

    .chat-messages {
        padding: 0.5rem;
    }

    .message-bubble {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    /* Auth Modal */
    .auth-tabs {
        font-size: 0.9rem;
    }

    .auth-tab {
        padding: 0.75rem;
    }

    /* Profile */
    .profile-avatar {
        width: 80px;
        height: 80px;
        font-size: 2.5rem;
    }

    .profile-name {
        font-size: 1.2rem;
    }

    .stat-value {
        font-size: 1.2rem;
    }

    /* Buttons */
    .submit-btn {
        padding: 0.75rem;
        font-size: 0.95rem;
    }

    .nav-btn span {
        display: none;
    }

    .nav-btn {
        padding: 0.6rem;
    }
}

/* Landscape Mode Adjustments */
@media (max-height: 600px) and (orientation: landscape) {
    .modal-content {
        max-height: 85vh;
    }

    .main-image {
        height: 150px;
    }

    .messages-modal .modal-content {
        height: 85vh;
    }

    .conversations-list {
        height: 120px;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Larger touch targets */
    .nav-btn,
    .mobile-menu-item,
    .product-card,
    .filter-select,
    .filter-input,
    .submit-btn,
    .contact-btn {
        min-height: 44px;
    }

    /* Remove hover effects on touch devices */
    .product-card:hover {
        transform: none;
    }

    .nav-btn:hover {
        border-color: var(--light-color);
        color: var(--dark-color);
    }

    /* Improve scrolling performance */
    .products-grid,
    .conversations-list,
    .chat-messages {
        -webkit-overflow-scrolling: touch;
    }
}

/* Print Styles */
@media print {
    .header,
    .filters-sidebar,
    .filter-toggle,
    .nav-menu,
    .mobile-menu,
    .modal,
    .toast {
        display: none !important;
    }

    .main-content {
        display: block;
    }

    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .product-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* High Resolution Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .product-image,
    .main-image,
    .thumbnail {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .product-card:hover {
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #ECF0F1;
        --light-color: #34495E;
        --gray-color: #95A5A6;
        --shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
        --shadow-lg: 0 5px 20px rgba(255, 255, 255, 0.15);
    }

    body {
        background-color: #1a1a1a;
        color: var(--dark-color);
    }

    .header,
    .product-card,
    .filters-sidebar,
    .modal-content {
        background-color: #2c2c2c;
    }

    .search-bar input,
    .filter-select,
    .filter-input,
    .form-group input,
    .form-group select,
    .form-group textarea {
        background-color: #3c3c3c;
        color: var(--dark-color);
        border-color: #4c4c4c;
    }

    .nav-btn {
        border-color: #4c4c4c;
    }

    .message.received .message-bubble {
        background-color: #3c3c3c;
        color: var(--dark-color);
    }
}
