/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern 2025 Color Palette - Light Theme */
    --primary-50: #EEF2FF;
    --primary-100: #E0E7FF;
    --primary-200: #C7D2FE;
    --primary-300: #A5B4FC;
    --primary-400: #818CF8;
    --primary-500: #6366F1;
    --primary-600: #4F46E5;
    --primary-700: #4338CA;
    --primary-800: #3730A3;
    --primary-900: #312E81;

    --secondary-50: #F0FDFA;
    --secondary-100: #CCFBF1;
    --secondary-200: #99F6E4;
    --secondary-300: #5EEAD4;
    --secondary-400: #2DD4BF;
    --secondary-500: #14B8A6;
    --secondary-600: #0D9488;
    --secondary-700: #0F766E;
    --secondary-800: #115E59;
    --secondary-900: #134E4A;

    --gray-50: #F8FAFC;
    --gray-100: #F1F5F9;
    --gray-200: #E2E8F0;
    --gray-300: #CBD5E1;
    --gray-400: #94A3B8;
    --gray-500: #64748B;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1E293B;
    --gray-900: #0F172A;

    --blue-50: #EFF6FF;
    --blue-100: #DBEAFE;
    --blue-200: #BFDBFE;
    --blue-300: #93C5FD;
    --blue-400: #60A5FA;
    --blue-500: #3B82F6;
    --blue-600: #2563EB;
    --blue-700: #1D4ED8;
    --blue-800: #1E40AF;
    --blue-900: #1E3A8A;

    /* Theme Colors */
    --primary-color: var(--primary-500);
    --primary-dark: var(--primary-600);
    --primary-light: var(--primary-400);
    --secondary-color: var(--secondary-500);
    --accent-color: var(--blue-500);

    --background: var(--gray-50);
    --surface: var(--gray-50);
    --card-background: #FFFFFF;
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-500);
    --border-color: var(--gray-200);
    --border-light: var(--gray-100);

    --success-color: #10B981;
    --warning-color: #F59E0B;
    --danger-color: #EF4444;
    --info-color: var(--blue-500);

    /* Modern Shadows with 2025 depth */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-2xl: 0 50px 100px -20px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

    /* Glassmorphism effects */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --backdrop-blur: blur(16px);

    /* Modern Transitions */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Modern Border Radius */
    --radius-xs: 0.25rem;
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;
    --radius-3xl: 3rem;
    --radius-full: 9999px;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
}

/* Dark Theme */
[data-theme="dark"] {
    --background: var(--gray-900);
    --surface: var(--gray-800);
    --card-background: var(--gray-800);
    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-400);
    --border-color: var(--gray-700);
    --border-light: var(--gray-600);

    --glass-bg: rgba(0, 0, 0, 0.25);
    --glass-border: rgba(255, 255, 255, 0.1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--background) 0%, var(--surface) 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: var(--transition);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection styles */
::selection {
    background-color: var(--primary-200);
    color: var(--primary-900);
}

/* Focus styles */
*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--card-background);
    backdrop-filter: var(--backdrop-blur);
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) 0;
    gap: var(--space-8);
}

.logo h1 {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--blue-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Poppins', sans-serif;
    font-size: 1.875rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    letter-spacing: -0.025em;
}

.logo h1 i {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--blue-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-bar {
    flex: 1;
    display: flex;
    max-width: 500px;
    position: relative;
}

.search-bar input {
    flex: 1;
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-full) 0 0 var(--radius-full);
    font-size: 0.95rem;
    outline: none;
    transition: var(--transition);
    background-color: var(--surface);
    color: var(--text-primary);
    font-weight: 400;
}

.search-bar input::placeholder {
    color: var(--text-muted);
}

.search-bar input:focus {
    border-color: var(--primary-color);
    background-color: var(--card-background);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.search-btn {
    padding: 0 var(--space-8);
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    border: none;
    border-radius: 0 var(--radius-full) var(--radius-full) 0;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.search-btn:hover::before {
    left: 100%;
}

.search-btn:hover {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.nav-menu {
    display: flex;
    gap: var(--space-3);
}

.nav-btn {
    position: relative;
    padding: var(--space-3) var(--space-5);
    background-color: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: var(--transition-bounce);
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    backdrop-filter: var(--backdrop-blur);
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--blue-600) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.nav-btn:hover::before {
    opacity: 0.1;
}

.nav-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-btn:active {
    transform: translateY(0);
}

.nav-btn .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, var(--danger-color) 0%, #DC2626 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    min-width: 20px;
    text-align: center;
    border: 2px solid var(--card-background);
    box-shadow: var(--shadow-sm);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Theme Toggle */
.theme-toggle {
    position: relative;
}

.theme-toggle i {
    transition: var(--transition);
}

[data-theme="dark"] .theme-toggle i:before {
    content: "\f185"; /* fa-sun */
}

/* Tooltip */
.tooltip {
    position: absolute;
    background: var(--gray-900);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.2s ease-out forwards;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--gray-900);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--dark-color);
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background-color: white;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    z-index: 2000;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--light-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-color);
}

.mobile-menu-content {
    padding: 1rem;
}

.mobile-menu-item {
    width: 100%;
    padding: 1rem;
    background: none;
    border: none;
    text-align: left;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    position: relative;
}

.mobile-menu-item:hover {
    background-color: var(--light-color);
}

/* Main Content */
.main-content {
    display: flex;
    gap: 2rem;
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 20px;
}

/* Filters Sidebar */
.filters-sidebar {
    width: 280px;
    background-color: white;
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    height: fit-content;
    position: sticky;
    top: 100px;
    border: 1px solid var(--gray-100);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
}

.filter-header h3 {
    color: var(--gray-900);
    font-size: 1.25rem;
    font-weight: 600;
}

.clear-filters {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
}

.clear-filters:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.filter-section {
    margin-bottom: 2rem;
}

.filter-section h4 {
    margin-bottom: 1rem;
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.filter-select,
.filter-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    outline: none;
    transition: var(--transition);
    background-color: var(--white);
}

.filter-select:focus,
.filter-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.price-input {
    flex: 1;
    padding: 0.875rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    outline: none;
    transition: var(--transition);
    background-color: var(--white);
}

.price-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.apply-filters {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 600;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.apply-filters:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Products Section */
.products-section {
    flex: 1;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--blue-600) 100%);
    border-radius: var(--radius-2xl);
    padding: var(--space-16) var(--space-8);
    margin-bottom: var(--space-12);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-content h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: var(--space-4);
    letter-spacing: -0.025em;
}

.hero-content p {
    font-size: 1.25rem;
    margin-bottom: var(--space-8);
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-12);
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Featured Categories */
.featured-categories {
    margin-bottom: var(--space-12);
}

.featured-categories h2 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-8);
    text-align: center;
    font-family: 'Poppins', sans-serif;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.category-card {
    background: var(--card-background);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-8) var(--space-4);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--blue-600) 100%);
    opacity: 0;
    transition: var(--transition);
}

.category-card:hover::before {
    opacity: 0.1;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-200);
}

.category-card i {
    font-size: 2.5rem;
    color: var(--primary-500);
    margin-bottom: var(--space-4);
    transition: var(--transition);
    position: relative;
    z-index: 1;
}

.category-card:hover i {
    color: var(--primary-600);
    transform: scale(1.1);
}

.category-card span {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    position: relative;
    z-index: 1;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-4);
    border-bottom: 2px solid var(--border-light);
    margin-top: var(--space-12);
}

.section-header:first-child {
    margin-top: 0;
}

.section-header h2 {
    color: var(--text-primary);
    font-size: 1.75rem;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
    letter-spacing: -0.025em;
}

.view-all-link {
    color: var(--primary-500);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: var(--transition);
}

.view-all-link:hover {
    color: var(--primary-600);
    transform: translateX(4px);
}

.section-controls {
    display: flex;
    gap: var(--space-4);
    align-items: center;
}

.sort-select {
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--card-background);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-toggle {
    display: none;
    padding: 0.875rem 1.25rem;
    background-color: white;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    color: var(--gray-700);
}

.filter-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(99, 102, 241, 0.05);
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-8);
    padding: var(--space-4) 0;
}

.product-card {
    background: var(--card-background);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-bounce);
    cursor: pointer;
    border: 1px solid var(--border-light);
    position: relative;
    backdrop-filter: var(--backdrop-blur);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--blue-600) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.product-card:hover::before {
    opacity: 0.03;
}

.product-image {
    width: 100%;
    height: 260px;
    object-fit: cover;
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    transition: var(--transition);
    position: relative;
}

.product-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.product-card:hover .product-image::after {
    opacity: 1;
}

.product-info {
    padding: var(--space-6);
    position: relative;
}

.product-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--space-3);
    color: var(--text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
    font-family: 'Poppins', sans-serif;
}

.product-price {
    font-size: 1.75rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--blue-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-4);
    font-family: 'Poppins', sans-serif;
    letter-spacing: -0.025em;
}

.product-location {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: var(--space-2);
    font-weight: 500;
}

.product-location i {
    font-size: 0.875rem;
    color: var(--primary-400);
}

.product-date {
    color: var(--text-muted);
    font-size: 0.8125rem;
    font-weight: 400;
}

.product-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid var(--border-light);
}

.favorite-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    transition: var(--transition-bounce);
    padding: var(--space-3);
    border-radius: var(--radius-full);
    position: relative;
    overflow: hidden;
}

.favorite-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, transparent 70%);
    transition: var(--transition);
    transform: translate(-50%, -50%);
    border-radius: var(--radius-full);
}

.favorite-btn:hover::before {
    width: 100%;
    height: 100%;
}

.favorite-btn:hover {
    color: var(--danger-color);
    transform: scale(1.1);
}

.favorite-btn.active {
    color: var(--danger-color);
    animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.15); }
    100% { transform: scale(1); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-16) var(--space-8);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    color: var(--gray-300);
    margin-bottom: var(--space-6);
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--space-3);
    font-family: 'Poppins', sans-serif;
}

.empty-state p {
    font-size: 1rem;
    line-height: 1.6;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.75);
    backdrop-filter: blur(4px);
    z-index: 3000;
    overflow-y: auto;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.modal-content {
    background-color: white;
    border-radius: var(--radius-xl);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-2xl);
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid var(--gray-100);
}

.modal-header h2 {
    color: var(--gray-900);
    font-size: 1.5rem;
    font-weight: 700;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-400);
    transition: var(--transition);
    padding: 0.5rem;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal:hover {
    color: var(--gray-700);
    background-color: var(--gray-100);
}

/* Forms */
.product-form,
.auth-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.75rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
    font-weight: 600;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    outline: none;
    transition: var(--transition);
    background-color: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.file-input {
    padding: 0.75rem;
    background-color: var(--gray-50);
    border: 2px dashed var(--gray-300);
    cursor: pointer;
}

.file-input:hover {
    border-color: var(--primary-color);
    background-color: rgba(99, 102, 241, 0.05);
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.preview-image {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    overflow: hidden;
    border-radius: var(--radius-md);
}

.preview-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger-color);
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.remove-image:hover {
    transform: scale(1.1);
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.submit-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:active {
    transform: translateY(0);
}

/* Product Detail */
.product-detail .modal-content {
    max-width: 900px;
}

.product-detail-content {
    padding: 2rem;
}

.detail-images {
    margin-bottom: 2rem;
}

.main-image {
    width: 100%;
    height: 450px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    margin-bottom: 1rem;
    background-color: var(--gray-100);
}

.thumbnail-images {
    display: flex;
    gap: 0.75rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
}

.thumbnail-images::-webkit-scrollbar {
    height: 6px;
}

.thumbnail-images::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

.thumbnail-images::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
}

.thumbnail {
    width: 90px;
    height: 90px;
    object-fit: cover;
    border-radius: var(--radius-md);
    cursor: pointer;
    opacity: 0.6;
    transition: var(--transition);
    border: 2px solid transparent;
    flex-shrink: 0;
}

.thumbnail.active {
    opacity: 1;
    border-color: var(--primary-color);
}

.thumbnail:hover {
    opacity: 1;
}

.detail-info h2 {
    color: var(--gray-900);
    margin-bottom: 1rem;
    font-size: 1.75rem;
    font-weight: 700;
}

.detail-price {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.detail-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--gray-100);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-600);
    font-size: 0.875rem;
}

.meta-item i {
    color: var(--gray-400);
}

.detail-description {
    margin-bottom: 2rem;
}

.detail-description h3 {
    margin-bottom: 1rem;
    color: var(--gray-900);
    font-size: 1.125rem;
    font-weight: 600;
}

.detail-description p {
    color: var(--gray-600);
    line-height: 1.8;
}

.seller-info {
    background-color: var(--gray-50);
    padding: 2rem;
    border-radius: var(--radius-lg);
    margin-bottom: 2rem;
    border: 1px solid var(--gray-100);
}

.seller-info h3 {
    margin-bottom: 1.5rem;
    color: var(--gray-900);
    font-size: 1.125rem;
    font-weight: 600;
}

.seller-details {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.seller-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
}

.seller-info-text h4 {
    color: var(--gray-900);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.seller-info-text p {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.contact-buttons {
    display: flex;
    gap: 1rem;
}

.contact-btn {
    flex: 1;
    padding: 0.875rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.9375rem;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.message-btn {
    background-color: var(--secondary-color);
    color: white;
}

.message-btn:hover {
    background-color: #0F9D8F;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.call-btn {
    background-color: var(--success-color);
    color: white;
}

.call-btn:hover {
    background-color: #0E9F6E;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Messages Modal */
.messages-modal .modal-content {
    max-width: 1000px;
    height: 650px;
}

.messages-container {
    display: flex;
    height: calc(100% - 80px);
}

.conversations-list {
    width: 320px;
    border-right: 1px solid var(--gray-100);
    overflow-y: auto;
    background-color: var(--gray-50);
}

.conversations-list::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track {
    background: var(--gray-50);
}

.conversations-list::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
}

.conversation-item {
    padding: 1.25rem;
    border-bottom: 1px solid var(--gray-100);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.conversation-item:hover {
    background-color: var(--gray-100);
}

.conversation-item.active {
    background-color: var(--white);
    border-left: 3px solid var(--primary-color);
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.conversation-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.9375rem;
}

.conversation-time {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.conversation-preview {
    color: var(--gray-600);
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.5;
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--white);
}

.chat-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
}

.chat-placeholder i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: var(--gray-300);
}

.chat-placeholder p {
    font-size: 1.125rem;
    color: var(--gray-500);
}

.chat-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--gray-100);
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.chat-header h3 {
    color: var(--gray-900);
    font-size: 1.125rem;
    font-weight: 600;
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    background-color: var(--gray-50);
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--gray-50);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
}

.message {
    margin-bottom: 1.25rem;
    display: flex;
    animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.sent {
    justify-content: flex-end;
}

.message-bubble {
    max-width: 65%;
    padding: 0.875rem 1.25rem;
    border-radius: 1.25rem;
    word-wrap: break-word;
    box-shadow: var(--shadow-sm);
}

.message.received .message-bubble {
    background-color: var(--white);
    color: var(--gray-900);
    border-bottom-left-radius: 0.25rem;
}

.message.sent .message-bubble {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.message-time {
    font-size: 0.6875rem;
    margin-top: 0.375rem;
    opacity: 0.7;
}

.chat-input {
    padding: 1.25rem;
    border-top: 1px solid var(--gray-100);
    display: flex;
    gap: 0.75rem;
    background-color: var(--white);
}

.chat-input input {
    flex: 1;
    padding: 0.875rem 1.25rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-full);
    outline: none;
    font-size: 0.9375rem;
    transition: var(--transition);
    background-color: var(--gray-50);
}

.chat-input input:focus {
    border-color: var(--primary-color);
    background-color: var(--white);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.send-btn {
    padding: 0 1.75rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.send-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.send-btn:active {
    transform: translateY(0);
}

/* Auth Modal */
.auth-modal .modal-content {
    max-width: 420px;
}

.auth-tabs {
    display: flex;
    border-bottom: 2px solid var(--gray-100);
}

.auth-tab {
    flex: 1;
    padding: 1.25rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9375rem;
    color: var(--gray-500);
    transition: var(--transition);
    border-bottom: 3px solid transparent;
    font-weight: 600;
    position: relative;
}

.auth-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.auth-tab:hover:not(.active) {
    color: var(--gray-700);
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

/* Profile Modal */
.profile-content {
    padding: 2rem;
}

.profile-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3.5rem;
    margin: 0 auto 1.5rem;
    box-shadow: var(--shadow-lg);
    font-weight: 600;
}

.profile-name {
    font-size: 1.75rem;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.profile-email {
    color: var(--gray-500);
    font-size: 0.9375rem;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
    margin-bottom: 2.5rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem 1rem;
    background-color: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-100);
    transition: var(--transition);
}

.stat-item:hover {
    background-color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 500;
}

.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.profile-btn {
    padding: 1rem 1.25rem;
    background-color: white;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.9375rem;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--gray-700);
}

.profile-btn:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-300);
    transform: translateX(2px);
}

.profile-btn i:last-child {
    color: var(--gray-400);
    transition: var(--transition);
}

.profile-btn:hover i:last-child {
    transform: translateX(4px);
}

.logout-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    margin-top: 0.5rem;
}

.logout-btn:hover {
    background-color: #DC2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Toast Notification */
.toast {
    position: fixed;
    bottom: 24px;
    right: 24px;
    padding: 1rem 1.5rem;
    background-color: var(--gray-900);
    color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
    transform: translateX(400px);
    transition: var(--transition);
    z-index: 4000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    max-width: 400px;
}

.toast.show {
    transform: translateX(0);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(400px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast i {
    font-size: 1.25rem;
}

.toast-message {
    flex: 1;
    font-size: 0.9375rem;
    line-height: 1.5;
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--danger-color);
}

.toast.warning {
    background-color: var(--warning-color);
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--gray-200);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 0.8s linear infinite;
}

.loading.large {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    color: var(--gray-300);
}

.empty-state h3 {
    color: var(--gray-900);
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.empty-state p {
    color: var(--gray-600);
    font-size: 0.9375rem;
    line-height: 1.6;
    max-width: 400px;
    margin: 0 auto;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.hidden {
    display: none !important;
}
