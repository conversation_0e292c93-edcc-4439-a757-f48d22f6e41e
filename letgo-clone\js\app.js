// Main Application Module

const app = {
    // Initialize application
    init() {
        this.setupEventListeners();
        this.initializeModals();
        this.checkAuthStatus();
        this.initializeTheme();
        this.initializeTooltips();
        this.initializeCategoryCards();
        this.loadFeaturedProducts();
    },

    // Setup global event listeners
    setupEventListeners() {
        // Mobile menu toggle
        document.getElementById('mobileMenuBtn').addEventListener('click', () => {
            document.getElementById('mobileMenu').classList.add('active');
        });

        document.getElementById('closeMobileMenu').addEventListener('click', () => {
            document.getElementById('mobileMenu').classList.remove('active');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            
            if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                mobileMenu.classList.remove('active');
            }
        });

        // Modal close buttons
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modalId = e.target.closest('.close-modal').dataset.modal;
                utils.modal.close(modalId);
            });
        });

        // Close modal when clicking outside
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    utils.modal.close(modal.id);
                }
            });
        });

        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                utils.modal.closeAll();
            }
        });

        // Prevent form submission on enter in search
        document.getElementById('searchInput').addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });

        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.querySelector('.search-btn');

        const performSearch = () => {
            const query = searchInput.value.trim();
            if (query) {
                if (window.products) {
                    window.products.searchProducts(query);
                }
            }
        };

        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Sort functionality
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                if (window.products) {
                    window.products.sortProducts(e.target.value);
                }
            });
        }
    },

    // Initialize modals
    initializeModals() {
        // Ensure modals are hidden on load
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('active');
        });
    },

    // Check authentication status on load
    checkAuthStatus() {
        const user = utils.getCurrentUser();
        if (user) {
            // Update UI for authenticated user
            if (window.auth) {
                window.auth.updateUIForAuth();
            }

            // Update message count
            if (window.messages) {
                window.messages.updateMessageCount();
            }
        }
    },

    // Initialize theme system
    initializeTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
    },

    // Toggle theme
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        this.updateThemeIcon(newTheme);

        utils.showToast(`${newTheme === 'dark' ? 'Karanlık' : 'Açık'} tema aktif`, 'info');
    },

    // Update theme icon
    updateThemeIcon(theme) {
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
    },

    // Initialize tooltips
    initializeTooltips() {
        utils.initTooltips();
    },

    // Initialize category cards
    initializeCategoryCards() {
        const categoryCards = document.querySelectorAll('.category-card');
        categoryCards.forEach(card => {
            card.addEventListener('click', () => {
                const category = card.dataset.category;
                if (category && window.products) {
                    window.products.filterByCategory(category);
                }
            });
        });
    },

    // Load featured products
    loadFeaturedProducts() {
        if (window.products) {
            // Load latest products
            window.products.loadLatestProducts();
            // Load popular products
            window.products.loadPopularProducts();
            // Load all products
            window.products.loadProducts();
        }
    }
};

// Service Worker Registration (for PWA)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js').catch(() => {
            // Service worker registration failed, app will still work without offline support
        });
    });
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    app.init();
});

// Handle online/offline status
window.addEventListener('online', () => {
    utils.showToast('İnternet bağlantısı yeniden kuruldu', 'success');
});

window.addEventListener('offline', () => {
    utils.showToast('İnternet bağlantısı kesildi', 'error');
});

// Lazy load images when they come into view
const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        }
    });
});

// Observe all images with data-src
document.addEventListener('DOMContentLoaded', () => {
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => imageObserver.observe(img));
});

// Export app object
window.app = app;
