// Authentication Module

const auth = {
    // Initialize authentication
    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
    },

    // Setup event listeners
    setupEventListeners() {
        // Auth tabs
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchAuthTab(e.target.dataset.tab);
            });
        });

        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Register form
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleRegister();
            });
        }

        // Profile button
        document.getElementById('profileBtn').addEventListener('click', () => {
            if (utils.isLoggedIn()) {
                this.showProfile();
            } else {
                utils.modal.open('authModal');
            }
        });

        // Mobile profile button
        document.getElementById('mobileProfile').addEventListener('click', () => {
            document.getElementById('mobileMenu').classList.remove('active');
            if (utils.isLoggedIn()) {
                this.showProfile();
            } else {
                utils.modal.open('authModal');
            }
        });

        // Logout button
        document.addEventListener('click', (e) => {
            if (e.target.closest('.logout-btn')) {
                this.logout();
            }
        });
    },

    // Switch between login and register tabs
    switchAuthTab(tab) {
        document.querySelectorAll('.auth-tab').forEach(t => {
            t.classList.toggle('active', t.dataset.tab === tab);
        });

        document.querySelectorAll('.auth-form').forEach(form => {
            form.classList.toggle('active', form.id === `${tab}Form`);
        });

        document.getElementById('authTitle').textContent = 
            tab === 'login' ? 'Giriş Yap' : 'Kayıt Ol';
    },

    // Handle login
    handleLogin() {
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;

        // Get users from storage
        const users = utils.storage.get('users') || [];
        
        // Find user
        const user = users.find(u => u.email === email && u.password === password);

        if (user) {
            // Set current user
            utils.storage.set('currentUser', {
                id: user.id,
                name: user.name,
                email: user.email,
                phone: user.phone,
                joinDate: user.joinDate
            });

            utils.modal.close('authModal');
            utils.showToast('Başarıyla giriş yaptınız!');
            this.updateUIForAuth();
            
            // Reset form
            document.getElementById('loginForm').reset();
            
            // Reload products to show user-specific features
            if (window.products) {
                window.products.loadProducts();
            }
        } else {
            utils.showToast('E-posta veya şifre hatalı!', 'error');
        }
    },

    // Handle registration
    handleRegister() {
        const name = document.getElementById('registerName').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        const phone = document.getElementById('registerPhone').value;

        // Validate
        if (!this.validateEmail(email)) {
            utils.showToast('Geçerli bir e-posta adresi giriniz!', 'error');
            return;
        }

        if (password.length < 6) {
            utils.showToast('Şifre en az 6 karakter olmalıdır!', 'error');
            return;
        }

        if (!this.validatePhone(phone)) {
            utils.showToast('Geçerli bir telefon numarası giriniz!', 'error');
            return;
        }

        // Get existing users
        const users = utils.storage.get('users') || [];

        // Check if email already exists
        if (users.some(u => u.email === email)) {
            utils.showToast('Bu e-posta adresi zaten kayıtlı!', 'error');
            return;
        }

        // Create new user
        const newUser = {
            id: utils.generateId(),
            name,
            email,
            password, // In real app, this should be hashed
            phone,
            joinDate: new Date().toISOString(),
            favorites: [],
            products: []
        };

        // Save user
        users.push(newUser);
        utils.storage.set('users', users);

        // Auto login
        utils.storage.set('currentUser', {
            id: newUser.id,
            name: newUser.name,
            email: newUser.email,
            phone: newUser.phone,
            joinDate: newUser.joinDate
        });

        utils.modal.close('authModal');
        utils.showToast('Kayıt başarılı! Hoş geldiniz!');
        this.updateUIForAuth();

        // Reset form
        document.getElementById('registerForm').reset();
    },

    // Logout
    logout() {
        utils.storage.remove('currentUser');
        utils.modal.close('profileModal');
        utils.showToast('Başarıyla çıkış yaptınız');
        this.updateUIForAuth();
        
        // Reload products
        if (window.products) {
            window.products.loadProducts();
        }
    },

    // Check authentication status
    checkAuthStatus() {
        this.updateUIForAuth();
    },

    // Update UI based on auth status
    updateUIForAuth() {
        const isAuthenticated = utils.isLoggedIn();
        const profileBtn = document.getElementById('profileBtn');
        
        if (isAuthenticated) {
            const user = utils.getCurrentUser();
            profileBtn.innerHTML = `<i class="fas fa-user-check"></i>`;
            profileBtn.title = user.name;
        } else {
            profileBtn.innerHTML = `<i class="fas fa-user"></i>`;
            profileBtn.title = 'Giriş Yap';
        }

        // Update favorites count
        this.updateFavoritesCount();
    },

    // Update favorites count
    updateFavoritesCount() {
        const user = utils.getCurrentUser();
        if (user) {
            const users = utils.storage.get('users') || [];
            const fullUser = users.find(u => u.id === user.id);
            const favCount = fullUser ? (fullUser.favorites || []).length : 0;
            
            document.getElementById('favoriteBadge').textContent = favCount;
            document.querySelector('#mobileFavorites .badge').textContent = favCount;
        } else {
            document.getElementById('favoriteBadge').textContent = '0';
            document.querySelector('#mobileFavorites .badge').textContent = '0';
        }
    },

    // Show profile
    showProfile() {
        const user = utils.getCurrentUser();
        if (!user) return;

        const users = utils.storage.get('users') || [];
        const fullUser = users.find(u => u.id === user.id);
        const products = utils.storage.get('products') || [];
        
        const userProducts = products.filter(p => p.userId === user.id);
        const favoriteCount = fullUser ? (fullUser.favorites || []).length : 0;

        const profileContent = `
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h3 class="profile-name">${user.name}</h3>
                <p class="profile-email">${user.email}</p>
            </div>
            
            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-value">${userProducts.length}</div>
                    <div class="stat-label">İlan</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${favoriteCount}</div>
                    <div class="stat-label">Favori</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${this.getDaysSinceJoin(user.joinDate)}</div>
                    <div class="stat-label">Gün</div>
                </div>
            </div>
            
            <div class="profile-actions">
                <button class="profile-btn" onclick="auth.showMyProducts()">
                    <span><i class="fas fa-box"></i> İlanlarım</span>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="profile-btn" onclick="auth.showMyFavorites()">
                    <span><i class="fas fa-heart"></i> Favorilerim</span>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="profile-btn" onclick="auth.editProfile()">
                    <span><i class="fas fa-edit"></i> Profili Düzenle</span>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="profile-btn logout-btn">
                    <span><i class="fas fa-sign-out-alt"></i> Çıkış Yap</span>
                </button>
            </div>
        `;

        document.getElementById('profileContent').innerHTML = profileContent;
        utils.modal.open('profileModal');
    },

    // Show user's products
    showMyProducts() {
        utils.modal.close('profileModal');
        if (window.products) {
            window.products.showUserProducts();
        }
    },

    // Show user's favorites
    showMyFavorites() {
        utils.modal.close('profileModal');
        if (window.products) {
            window.products.showFavorites();
        }
    },

    // Edit profile (placeholder)
    editProfile() {
        utils.showToast('Bu özellik yakında eklenecek!', 'warning');
    },

    // Helper functions
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    validatePhone(phone) {
        const re = /^[0-9]{10}$/;
        return re.test(phone.replace(/\s/g, ''));
    },

    getDaysSinceJoin(joinDate) {
        const days = Math.floor((new Date() - new Date(joinDate)) / (1000 * 60 * 60 * 24));
        return days || 1;
    }
};

// Initialize auth when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    auth.init();
});

// Export auth object
window.auth = auth;
